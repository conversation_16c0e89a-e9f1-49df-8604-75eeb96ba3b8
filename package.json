{"name": "hreff", "version": "1.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "lint-staged": "lint-staged", "format": "prettier --check .", "format:fix": "prettier --write --list-different .", "prepare": "husky"}, "dependencies": {"gray-matter": "^4.0.3", "next": "13.4.3", "react": "18.2.0", "react-dom": "18.2.0", "remark-html": "^15.0.2", "sass": "^1.49.7", "slugify": "^1.6.6", "swc": "^1.0.11", "swiper": "^9.0.0", "zod": "^3.24.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@commitlint/types": "^19.8.1", "@eslint/js": "^9.27.0", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "conventional-changelog-atom": "^5.0.0", "eslint": "^8.57.1", "eslint-config-next": "^15.3.2", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-tailwindcss": "^3.18.0", "husky": "^9.1.7", "lint-staged": "^16.0.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "raw-loader": "^4.0.2", "remark": "^14.0.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}}