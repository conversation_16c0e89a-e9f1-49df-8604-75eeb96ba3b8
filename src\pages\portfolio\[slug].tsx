import { GetStaticPaths, GetStaticProps } from 'next';
import { getProjectData, getSortedProjectsData } from '@/src/lib/projects';
import { PostFull } from '@/src/lib/type';
import PageBanner from '@/src/components/PageBanner';
import Layouts from '@/src/layouts/Layouts';
import Link from 'next/link';
import Image from 'next/image';
import slugify from 'slugify';

interface ProjectPageProps {
  project: PostFull;
}

const ProjectPage = ({ project }: ProjectPageProps) => {
  return (
    <Layouts>
      <PageBanner pageName={'Projet'} pageTitle={project.title} />

      {/* project details */}
      <section className="mil-p-120-60">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col justify-between gap-8 lg:flex-row">
            <div className="w-full lg:w-2/3 xl:w-2/3">
              {/* project image */}
              <div className="mil-image-frame mil-mb-60">
                <Image
                  src={`/${project.image}`}
                  alt={project.title as string}
                  fill
                />
              </div>

              {/* project content */}
              <div
                className="mil-project-content"
                dangerouslySetInnerHTML={{ __html: project.contentHtml }}
              />
            </div>

            <div className="w-full lg:w-1/3 xl:w-1/3">
              <div className="mil-sidebar">
                {/* project info */}
                <div className="mil-widget mil-mb-60">
                  <h5 className="mil-mb-30">Informations du Projet</h5>
                  <ul className="mil-simple-list">
                    <li>
                      <strong>Client:</strong> {project.author}
                    </li>
                    <li>
                      <strong>Catégorie:</strong> {project.tags?.join(', ')}
                    </li>
                    <li>
                      <strong>Statut:</strong> Terminé
                    </li>
                  </ul>
                </div>

                {/* project summary */}
                <div className="mil-widget mil-mb-60">
                  <h5 className="mil-mb-30">Résumé</h5>
                  <p>{project.short as string}</p>
                </div>

                {/* call to action */}
                <div className="mil-widget">
                  <h5 className="mil-mb-30">
                    Intéressé par un projet similaire ?
                  </h5>
                  <p className="mil-mb-30">
                    Contactez-nous pour discuter de vos besoins et découvrir
                    comment nous pouvons vous aider.
                  </p>
                  <Link href="/contact" className="mil-button mil-border">
                    <span>Nous Contacter</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* related projects */}
      <section className="mil-deep-bg mil-p-120-60">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="mil-mb-90 flex flex-col items-center justify-between gap-6 xl:flex-row">
            <div className="w-full xl:w-1/2">
              <h2>
                Autres <span className="mil-accent">Projets</span>
              </h2>
            </div>
            <div className="w-full xl:w-5/12">
              <p>
                Découvrez d'autres réalisations de HREFF SARL dans différents
                domaines.
              </p>
            </div>
          </div>

          <div className="flex">
            <div className="w-full">
              <Link href="/portfolio" className="mil-button mil-border">
                <span>Voir Tous les Projets</span>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </Layouts>
  );
};

export default ProjectPage;

export const getStaticPaths: GetStaticPaths = async () => {
  const projects = getSortedProjectsData();
  const paths = projects.map((project) => ({
    params: { slug: slugify(project.title, { lower: true }) },
  }));

  return {
    paths,
    fallback: false,
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const project = await getProjectData(params!.slug as string);

  return {
    props: {
      project,
    },
  };
};
