import { GetStaticPaths, GetStaticProps } from 'next';
import { getProjectData, getSortedProjectsData } from '@/src/lib/projects';
import { PostFull } from '@/src/lib/type';
import PageBanner from '@/src/components/PageBanner';
import Layouts from '@/src/layouts/Layouts';
import Link from 'next/link';
import Image from 'next/image';

interface ProjectPageProps {
  project: Omit<PostFull, 'date'>;
}

const ProjectPage = ({ project }: ProjectPageProps) => {
  return (
    <Layouts>
      <PageBanner pageName={'Projet'} pageTitle={project.title} />

      {/* project details */}
      <section className="mil-p-120-60">
        <div className="container">
          <div className="row justify-content-between">
            <div className="col-lg-7 col-xl-8">
              {/* project image */}
              <div className="mil-image-frame mil-mb-60">
                <Image
                  src={`/${project.image}`}
                  alt={project.title as string}
                  fill
                />
              </div>

              {/* project content */}
              <div
                className="mil-project-content"
                dangerouslySetInnerHTML={{ __html: project.contentHtml }}
              />
            </div>

            <div className="col-lg-5 col-xl-4">
              <div className="mil-sidebar">
                {/* project info */}
                <div className="mil-widget mil-mb-60">
                  <h5 className="mil-mb-30">Informations du Projet</h5>
                  <ul className="mil-simple-list">
                    <li>
                      <strong>Client:</strong> {project.author}
                    </li>
                    <li>
                      <strong>Catégorie:</strong> {project.tags?.join(', ')}
                    </li>
                    <li>
                      <strong>Statut:</strong> Terminé
                    </li>
                  </ul>
                </div>

                {/* project summary */}
                <div className="mil-widget mil-mb-60">
                  <h5 className="mil-mb-30">Résumé</h5>
                  <p>{project.short}</p>
                </div>

                {/* call to action */}
                <div className="mil-widget">
                  <h5 className="mil-mb-30">
                    Intéressé par un projet similaire ?
                  </h5>
                  <p className="mil-mb-30">
                    Contactez-nous pour discuter de vos besoins et découvrir
                    comment nous pouvons vous aider.
                  </p>
                  <Link href="/contact" className="mil-button mil-border">
                    <span>Nous Contacter</span>
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* related projects */}
      <section className="mil-deep-bg mil-p-120-60">
        <div className="container">
          <div className="row align-items-center justify-content-between mil-mb-90">
            <div className="col-xl-6">
              <h2>
                Autres <span className="mil-accent">Projets</span>
              </h2>
            </div>
            <div className="col-xl-5">
              <p>
                Découvrez d'autres réalisations de HREFF SARL dans différents
                domaines.
              </p>
            </div>
          </div>

          <div className="row">
            <div className="col-12">
              <Link href="/portfolio" className="mil-button mil-border">
                <span>Voir Tous les Projets</span>
              </Link>
            </div>
          </div>
        </div>
      </section>
    </Layouts>
  );
};

export default ProjectPage;

export const getStaticPaths: GetStaticPaths = async () => {
  const projects = getSortedProjectsData();
  const paths = projects.map((project) => ({
    params: { slug: project.id },
  }));

  return {
    paths,
    fallback: false,
  };
};

export const getStaticProps: GetStaticProps = async ({ params }) => {
  const project = await getProjectData(params!.slug as string);

  return {
    props: {
      project,
    },
  };
};
