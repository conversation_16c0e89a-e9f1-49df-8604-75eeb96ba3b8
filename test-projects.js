const fs = require('fs');
const path = require('path');
const matter = require('gray-matter');

// Test parsing of project files
const projectsDir = path.join(__dirname, 'src/data/projects');
const files = fs.readdirSync(projectsDir);

console.log('Testing project files parsing...\n');

files.forEach(file => {
  if (file.endsWith('.md')) {
    console.log(`Testing ${file}:`);
    try {
      const filePath = path.join(projectsDir, file);
      const fileContent = fs.readFileSync(filePath, 'utf8');
      const result = matter(fileContent);
      
      console.log('✅ YAML parsed successfully');
      console.log('Title:', result.data.title);
      console.log('Tags:', result.data.tags);
      console.log('Author:', result.data.author);
      console.log('---\n');
    } catch (error) {
      console.log('❌ Error parsing YAML:', error.message);
      console.log('---\n');
    }
  }
});
